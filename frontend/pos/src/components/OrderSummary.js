import React from 'react';

function OrderSummary({ cartItems, total, onCheckout }) {
  return (
    <div className="order-summary">
      <h3>Your Order</h3>
      <ul className="cart-items">
        {cartItems.map(item => (
          <li key={item.id}>
            {item.name} x {item.quantity} - ${item.price * item.quantity}
          </li>
        ))}
      </ul>
      <div className="order-total">
        <strong>Total: ${total.toFixed(2)}</strong>
      </div>
      <button onClick={onCheckout} disabled={cartItems.length === 0}>Checkout</button>
    </div>
  );
}

export default OrderSummary;
