import React from 'react';

function Menu({ menuItems, onAddToCart }) {
  return (
    <div className="menu">
      <h3>Available Items</h3>
      <div className="menu-items">
        {menuItems.map(item => (
          <div key={item.id} className="menu-item">
            <h4>{item.name}</h4>
            <p>{item.description}</p>
            <p>${item.price}</p>
            <button onClick={() => onAddToCart(item)}>Add to Cart</button>
          </div>
        ))}
      </div>
    </div>
  );
}

export default Menu;
