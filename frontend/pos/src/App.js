import React, { useState, useEffect } from 'react';
import Menu from './components/Menu';
import OrderSummary from './components/OrderSummary';
import './App.css';

const API_BASE_URL = 'http://localhost:3001/api';

function App() {
  const [menuItems, setMenuItems] = useState([]);
  const [cartItems, setCartItems] = useState([]);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    fetchMenuItems();
  }, []);

  useEffect(() => {
    const newTotal = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    setTotal(newTotal);
  }, [cartItems]);

  const fetchMenuItems = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/menu`);
      const data = await response.json();
      setMenuItems(data);
    } catch (error) {
      console.error('Error fetching menu items:', error);
    }
  };

  const handleAddToCart = (item) => {
    setCartItems(prevItems => {
      const existingItem = prevItems.find(cartItem => cartItem.id === item.id);
      if (existingItem) {
        return prevItems.map(cartItem =>
          cartItem.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      } else {
        return [...prevItems, { ...item, quantity: 1 }];
      }
    });
  };

  const handleCheckout = async () => {
    try {
      const orderItems = cartItems.map(item => ({
        menu_item_id: item.id,
        quantity: item.quantity,
        price: item.price,
      }));

      const response = await fetch(`${API_BASE_URL}/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customer_name: 'Walk-in Customer', // For simplicity, can be dynamic later
          total_amount: total,
          status: 'pending',
          items: orderItems,
        }),
      });

      if (response.ok) {
        alert('Order placed successfully!');
        setCartItems([]);
      } else {
        alert('Failed to place order.');
      }
    } catch (error) {
      console.error('Error placing order:', error);
      alert('Error placing order.');
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Breakfast POS System</h1>
      </header>
      <main className="App-main">
        <div className="menu-section">
          <h2>Menu</h2>
          <Menu menuItems={menuItems} onAddToCart={handleAddToCart} />
        </div>
        <div className="order-section">
          <h2>Current Order</h2>
          <OrderSummary cartItems={cartItems} total={total} onCheckout={handleCheckout} />
        </div>
      </main>
    </div>
  );
}

export default App;
