# Breakfast POS + KDS System

This is a full-stack application for a breakfast shop, including a Point of Sale (POS) system, a Kitchen Display System (KDS), and an admin panel.

## Project Structure

- `backend/`: Node.js (Express) backend for API services and WebSocket communication.
- `frontend/`:
  - `pos/`: React application for the POS system.
  - `kds/`: React application for the KDS system.
  - `admin/`: React application for the admin panel.
- `docker-compose.yml`: Docker Compose configuration for setting up the development environment (backend, PostgreSQL, Redis).

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Node.js and npm (for frontend development outside Docker)

### Setup and Run

1.  **Clone the repository (if not already cloned):**

    ```bash
    git clone <repository-url>
    cd breakfast-pos
    ```

2.  **Start the backend services with Docker Compose:**

    Navigate to the root directory of the project (`breakfast-pos/`) and run:

    ```bash
    docker-compose up --build
    ```

    This will:
    - Build the `backend` Docker image.
    - Start the `backend` service (Node.js server).
    - Start the `db` service (PostgreSQL database).
    - Start the `redis` service (Redis cache).

    The backend server will be accessible at `http://localhost:3001`.

3.  **Run the frontend applications:**

    Each frontend application (`pos`, `kds`, `admin`) needs to be run separately.

    **For POS:**
    ```bash
    cd frontend/pos
    npm install
    npm start
    ```
    The POS application will typically run on `http://localhost:3000`.

    **For KDS:**
    ```bash
    cd frontend/kds
    npm install
    npm start
    ```
    The KDS application will typically run on `http://localhost:3000` (or another available port if POS is already running).

    **For Admin:**
    ```bash
    cd frontend/admin
    npm install
    npm start
    ```
    The Admin application will typically run on `http://localhost:3000` (or another available port).

    *Note: You might need to adjust the ports for frontend applications if they conflict, or run them on different machines/tabs.*

### Database Setup

After starting the Docker Compose services, you need to create the database schema.

1.  **Enter the PostgreSQL container:**
    ```bash
    docker-compose exec db bash
    ```
2.  **Execute the schema SQL file:**
    ```bash
    psql -U user -d pos_db -f /usr/src/app/database/schema.sql
    ```
3.  **Exit the container:**
    ```bash
    exit
    ```

## Technologies Used

- **Frontend:** React.js
- **Backend:** Node.js, Express.js, Socket.IO
- **Database:** PostgreSQL
- **Cache/Real-time:** Redis
- **Containerization:** Docker, Docker Compose

## Next Steps

- Implement remaining API endpoints in the backend (orders, users, etc.).
- Connect frontend applications to the backend API and WebSocket.
- Develop UI components for POS, KDS, and Admin.
