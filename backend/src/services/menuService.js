const db = require('../config/db');

const MenuService = {
  async getAllItems() {
    const { rows } = await db.query('SELECT * FROM menu_items ORDER BY id ASC');
    return rows;
  },

  async getItemById(id) {
    const { rows } = await db.query('SELECT * FROM menu_items WHERE id = $1', [id]);
    return rows[0];
  },

  async createItem({ name, description, price, category, is_available }) {
    const { rows } = await db.query(
      'INSERT INTO menu_items (name, description, price, category, is_available) VALUES ($1, $2, $3, $4, $5) RETURNING *'
      , [name, description, price, category, is_available]
    );
    return rows[0];
  },

  async updateItem(id, { name, description, price, category, is_available }) {
    const { rows } = await db.query(
      'UPDATE menu_items SET name = $1, description = $2, price = $3, category = $4, is_available = $5 WHERE id = $6 RETURNING *'
      , [name, description, price, category, is_available, id]
    );
    return rows[0];
  },

  async deleteItem(id) {
    const { rowCount } = await db.query('DELETE FROM menu_items WHERE id = $1', [id]);
    return rowCount > 0;
  },
};

module.exports = MenuService;
