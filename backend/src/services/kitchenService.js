const db = require('../config/db');

const KitchenService = {
  async getPendingOrders() {
    const { rows } = await db.query('SELECT * FROM orders WHERE status = $1 ORDER BY created_at ASC', ['pending']);
    return rows;
  },

  async getOrderDetails(orderId) {
    const { rows: orderRows } = await db.query('SELECT * FROM orders WHERE id = $1', [orderId]);
    if (!orderRows[0]) return null;

    const { rows: itemsRows } = await db.query(
      'SELECT oi.*, mi.name as item_name, mi.category as item_category FROM order_items oi JOIN menu_items mi ON oi.menu_item_id = mi.id WHERE oi.order_id = $1'
      , [orderId]
    );
    return { ...orderRows[0], items: itemsRows };
  },

  // This function could be extended to filter by kitchen station/category
  async getOrdersForKitchenDisplay(category = null) {
    let query = 'SELECT o.*, oi.quantity, mi.name as item_name, mi.category as item_category FROM orders o JOIN order_items oi ON o.id = oi.order_id JOIN menu_items mi ON oi.menu_item_id = mi.id WHERE o.status = $1';
    const params = ['pending'];

    if (category) {
      query += ' AND mi.category = $2';
      params.push(category);
    }
    query += ' ORDER BY o.created_at ASC';

    const { rows } = await db.query(query, params);

    // Group items by order
    const ordersMap = new Map();
    rows.forEach(row => {
      if (!ordersMap.has(row.id)) {
        ordersMap.set(row.id, { ...row, items: [] });
      }
      ordersMap.get(row.id).items.push({
        item_name: row.item_name,
        item_category: row.item_category,
        quantity: row.quantity,
      });
      // Remove redundant fields from the main order object
      delete ordersMap.get(row.id).quantity;
      delete ordersMap.get(row.id).item_name;
      delete ordersMap.get(row.id).item_category;
    });

    return Array.from(ordersMap.values());
  },
};

module.exports = KitchenService;
