const db = require('../config/db');

module.exports = (io) => ({
  async createOrder({ customer_name, total_amount, status, items }) {
    const client = await db.query('BEGIN');
    try {
      const { rows: orderRows } = await db.query(
        'INSERT INTO orders (customer_name, total_amount, status) VALUES ($1, $2, $3) RETURNING *'
        , [customer_name, total_amount, status]
      );
      const order = orderRows[0];

      for (const item of items) {
        await db.query(
          'INSERT INTO order_items (order_id, menu_item_id, quantity, price) VALUES ($1, $2, $3, $4)'
          , [order.id, item.menu_item_id, item.quantity, item.price]
        );
      }

      await db.query('COMMIT');
      io.emit('newOrder', order); // Emit new order event
      return order;
    } catch (e) {
      await db.query('ROLLBACK');
      throw e;
    }
  },

  async getAllOrders() {
    const { rows } = await db.query('SELECT * FROM orders ORDER BY created_at DESC');
    return rows;
  },

  async getOrderById(id) {
    const { rows: orderRows } = await db.query('SELECT * FROM orders WHERE id = $1', [id]);
    if (!orderRows[0]) return null;

    const { rows: itemsRows } = await db.query('SELECT * FROM order_items WHERE order_id = $1', [id]);
    return { ...orderRows[0], items: itemsRows };
  },

  async updateOrderStatus(id, status) {
    const { rows } = await db.query(
      'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *'
      , [status, id]
    );
    const updatedOrder = rows[0];
    if (updatedOrder) {
      io.emit('orderStatusUpdated', updatedOrder); // Emit order status updated event
    }
    return updatedOrder;
  },

  async deleteOrder(id) {
    const { rowCount } = await db.query('DELETE FROM orders WHERE id = $1', [id]);
    return rowCount > 0;
  },
});