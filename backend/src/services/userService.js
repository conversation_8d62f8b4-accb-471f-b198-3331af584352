const db = require('../config/db');

const UserService = {
  async createUser({ username, password, role }) {
    // In a real application, you would hash the password here
    const { rows } = await db.query(
      'INSERT INTO users (username, password, role) VALUES ($1, $2, $3) RETURNING id, username, role'
      , [username, password, role]
    );
    return rows[0];
  },

  async findUserByUsername(username) {
    const { rows } = await db.query('SELECT * FROM users WHERE username = $1', [username]);
    return rows[0];
  },
};

module.exports = UserService;
