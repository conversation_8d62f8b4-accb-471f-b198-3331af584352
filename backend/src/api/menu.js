const express = require('express');
const router = express.Router();
const menuService = require('../services/menuService');

// Get all menu items
router.get('/', async (req, res) => {
  try {
    const items = await menuService.getAllItems();
    res.json(items);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Get a single menu item by ID
router.get('/:id', async (req, res) => {
  try {
    const item = await menuService.getItemById(req.params.id);
    if (!item) {
      return res.status(404).json({ msg: 'Menu item not found' });
    }
    res.json(item);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Create a new menu item
router.post('/', async (req, res) => {
  try {
    const newItem = await menuService.createItem(req.body);
    res.status(201).json(newItem);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Update a menu item
router.put('/:id', async (req, res) => {
  try {
    const updatedItem = await menuService.updateItem(req.params.id, req.body);
    if (!updatedItem) {
      return res.status(404).json({ msg: 'Menu item not found' });
    }
    res.json(updatedItem);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Delete a menu item
router.delete('/:id', async (req, res) => {
  try {
    const deleted = await menuService.deleteItem(req.params.id);
    if (!deleted) {
      return res.status(404).json({ msg: 'Menu item not found' });
    }
    res.json({ msg: 'Menu item removed' });
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;
