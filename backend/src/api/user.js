const express = require('express');
const router = express.Router();
const userService = require('../services/userService');

// Register a new user
router.post('/register', async (req, res) => {
  try {
    const { username, password, role } = req.body;
    if (!username || !password || !role) {
      return res.status(400).json({ msg: 'Please enter all fields' });
    }

    const existingUser = await userService.findUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ msg: 'User already exists' });
    }

    const newUser = await userService.createUser({ username, password, role });
    res.status(201).json({ msg: 'User registered successfully', user: newUser });
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// User login (simplified - no JWT for now)
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    if (!username || !password) {
      return res.status(400).json({ msg: 'Please enter all fields' });
    }

    const user = await userService.findUserByUsername(username);
    if (!user) {
      return res.status(400).json({ msg: 'Invalid credentials' });
    }

    // In a real application, you would compare hashed passwords
    if (user.password !== password) {
      return res.status(400).json({ msg: 'Invalid credentials' });
    }

    res.json({ msg: 'Logged in successfully', user: { id: user.id, username: user.username, role: user.role } });
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;
