const express = require('express');
const router = express.Router();
let orderService;

module.exports = (io) => {
  orderService = require('../services/orderService')(io);

  // Create a new order
  router.post('/', async (req, res) => {
    try {
      const newOrder = await orderService.createOrder(req.body);
      res.status(201).json(newOrder);
    } catch (err) {
      console.error(err);
      res.status(500).send('Server Error');
    }
  });

  // Get all orders
  router.get('/', async (req, res) => {
    try {
      const orders = await orderService.getAllOrders();
      res.json(orders);
    } catch (err) {
      console.error(err);
      res.status(500).send('Server Error');
    }
  });

  // Get a single order by ID
  router.get('/:id', async (req, res) => {
    try {
      const order = await orderService.getOrderById(req.params.id);
      if (!order) {
        return res.status(404).json({ msg: 'Order not found' });
      }
      res.json(order);
    } catch (err) {
      console.error(err);
      res.status(500).send('Server Error');
    }
  });

  // Update order status
  router.put('/:id/status', async (req, res) => {
    try {
      const { status } = req.body;
      const updatedOrder = await orderService.updateOrderStatus(req.params.id, status);
      if (!updatedOrder) {
        return res.status(404).json({ msg: 'Order not found' });
      }
      res.json(updatedOrder);
    } catch (err) {
      console.error(err);
      res.status(500).send('Server Error');
    }
  });

  // Delete an order
  router.delete('/:id', async (req, res) => {
    try {
      const deleted = await orderService.deleteOrder(req.params.id);
      if (!deleted) {
        return res.status(404).json({ msg: 'Order not found' });
      }
      res.json({ msg: 'Order removed' });
    } catch (err) {
      console.error(err);
      res.status(500).send('Server Error');
    }
  });

  return router;
};