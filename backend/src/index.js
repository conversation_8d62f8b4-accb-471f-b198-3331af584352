const express = require('express');
const http = require('http');
const { Server } = require("socket.io");

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*", // Allow all origins for now, refine in production
    methods: ["GET", "POST"]
  }
});

const menuRoutes = require('./api/menu');
const orderRoutes = require('./api/order')(io); // Pass io instance
const userRoutes = require('./api/user');
const kitchenService = require('./services/kitchenService');

const PORT = process.env.PORT || 3001;

// Middleware to parse JSON bodies
app.use(express.json());

// API Routes
app.use('/api/menu', menuRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/users', userRoutes);

app.get('/', (req, res) => {
  res.send('<h1>POS Backend</h1>');
});

io.on('connection', async (socket) => {
  console.log('a user connected');

  // When a KDS client connects, send them the current pending orders
  try {
    const pendingOrders = await kitchenService.getOrdersForKitchenDisplay();
    socket.emit('initialOrders', pendingOrders);
  } catch (error) {
    console.error('Error fetching initial orders for KDS:', error);
  }

  socket.on('disconnect', () => {
    console.log('user disconnected');
  });

  // Example: KDS client marks an order as complete
  socket.on('orderComplete', async (orderId) => {
    try {
      const updatedOrder = await orderRoutes.orderService.updateOrderStatus(orderId, 'completed');
      // No need to emit here, orderService already emits 'orderStatusUpdated'
    } catch (error) {
      console.error(`Error marking order ${orderId} as complete:`, error);
    }
  });
});

server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
